# LifeM 项目结构设计

## 推荐的文件组织结构

```
lifem/
├── game/
│   ├── core/                          # 核心系统模块
│   │   ├── __init__.py
│   │   ├── game_engine.py             # 游戏引擎核心
│   │   ├── world_manager.py           # 世界管理系统
│   │   ├── npc_system.py              # NPC行为系统
│   │   ├── economic_system.py         # 经济系统
│   │   ├── ai_integration.py          # AI集成系统
│   │   ├── data_manager.py            # 数据管理
│   │   ├── save_system.py             # 存档系统
│   │   ├── async_manager.py           # 异步任务管理
│   │   └── memory_manager.py          # 内存管理
│   │
│   ├── data/                          # 数据模型
│   │   ├── __init__.py
│   │   ├── npc_models.py              # NPC数据模型
│   │   ├── world_models.py            # 世界数据模型
│   │   ├── economic_models.py         # 经济数据模型
│   │   ├── event_models.py            # 事件数据模型
│   │   └── relationship_models.py     # 关系数据模型
│   │
│   ├── ai/                            # AI相关模块
│   │   ├── __init__.py
│   │   ├── dialogue_ai.py             # 对话AI
│   │   ├── decision_ai.py             # 决策AI
│   │   ├── content_generation.py      # 内容生成
│   │   ├── tts_system.py              # 语音合成
│   │   ├── image_generation.py        # 图像生成
│   │   └── video_generation.py        # 视频生成
│   │
│   ├── systems/                       # 游戏系统
│   │   ├── __init__.py
│   │   ├── map_system.py              # 地图系统
│   │   ├── building_system.py         # 建筑系统
│   │   ├── event_system.py            # 事件系统
│   │   ├── relationship_system.py     # 关系系统
│   │   ├── time_system.py             # 时间系统
│   │   └── weather_system.py          # 天气系统
│   │
│   ├── ui/                            # 用户界面
│   │   ├── screens/                   # Ren'Py屏幕文件
│   │   │   ├── main_menu.rpy
│   │   │   ├── world_map.rpy
│   │   │   ├── npc_interaction.rpy
│   │   │   ├── economic_panel.rpy
│   │   │   ├── character_creation.rpy
│   │   │   └── sandbox_editor.rpy
│   │   │
│   │   ├── styles/                    # 样式定义
│   │   │   ├── main_styles.rpy
│   │   │   ├── dialogue_styles.rpy
│   │   │   └── ui_styles.rpy
│   │   │
│   │   └── components/                # UI组件
│   │       ├── dynamic_buttons.py
│   │       ├── data_displays.py
│   │       └── interactive_maps.py
│   │
│   ├── config/                        # 配置文件
│   │   ├── game_config.py             # 游戏配置
│   │   ├── ai_config.py               # AI配置
│   │   ├── economic_config.py         # 经济配置
│   │   └── world_config.py            # 世界配置
│   │
│   ├── utils/                         # 工具函数
│   │   ├── __init__.py
│   │   ├── math_utils.py              # 数学工具
│   │   ├── string_utils.py            # 字符串工具
│   │   ├── file_utils.py              # 文件工具
│   │   ├── cache_utils.py             # 缓存工具
│   │   └── debug_utils.py             # 调试工具
│   │
│   ├── assets/                        # 游戏资源
│   │   ├── images/                    # 图片资源
│   │   │   ├── backgrounds/
│   │   │   ├── characters/
│   │   │   ├── ui/
│   │   │   └── generated/             # AI生成的图片
│   │   │
│   │   ├── audio/                     # 音频资源
│   │   │   ├── music/
│   │   │   ├── sounds/
│   │   │   └── voices/                # AI生成的语音
│   │   │
│   │   └── videos/                    # 视频资源
│   │       └── generated/             # AI生成的视频
│   │
│   ├── database/                      # 数据库文件
│   │   ├── game_data.db               # 主数据库
│   │   ├── ai_cache.db                # AI缓存数据库
│   │   └── user_data.db               # 用户数据数据库
│   │
│   ├── saves/                         # 存档目录
│   │   └── (动态生成的存档文件)
│   │
│   ├── logs/                          # 日志目录
│   │   ├── game.log
│   │   ├── ai.log
│   │   └── error.log
│   │
│   ├── script.rpy                     # 主脚本文件
│   ├── options.rpy                    # 游戏选项
│   ├── gui.rpy                        # GUI配置
│   ├── screens.rpy                    # 基础屏幕
│   └── init.rpy                       # 初始化脚本
│
├── docs/                              # 文档目录
│   ├── project_requirements.md        # 项目需求文档
│   ├── project_structure.md           # 项目结构说明
│   ├── api_documentation.md           # API文档
│   ├── development_guide.md           # 开发指南
│   └── user_manual.md                 # 用户手册
│
├── tests/                             # 测试目录
│   ├── unit_tests/                    # 单元测试
│   ├── integration_tests/             # 集成测试
│   └── performance_tests/             # 性能测试
│
├── tools/                             # 开发工具
│   ├── data_generator.py              # 数据生成工具
│   ├── ai_test_client.py              # AI测试客户端
│   ├── performance_profiler.py        # 性能分析工具
│   └── database_manager.py            # 数据库管理工具
│
├── requirements.txt                   # Python依赖
├── README.md                          # 项目说明
├── LICENSE                            # 许可证
└── .gitignore                         # Git忽略文件
```

## 核心文件说明

### 1. game/script.rpy - 主入口文件
```python
# 游戏主入口点
define config.name = "LifeM"
define config.version = "1.0"

# 导入核心系统
init python:
    import sys
    sys.path.append(renpy.config.gamedir + "/core")
    sys.path.append(renpy.config.gamedir + "/data")
    sys.path.append(renpy.config.gamedir + "/systems")
    
    from game_engine import LifeMGameEngine
    
    # 初始化游戏引擎
    game_engine = LifeMGameEngine()

# 游戏开始标签
label start:
    # 初始化游戏系统
    $ game_engine.initialize()
    
    # 显示主菜单
    call screen main_menu
    
    return
```

### 2. game/init.rpy - 初始化配置
```python
# 游戏初始化配置
init -1 python:
    # 配置AI服务
    config.ai_services = {
        "local_llm": {
            "enabled": True,
            "model_path": "models/local_llm",
            "max_tokens": 2048
        },
        "cloud_apis": {
            "openai": {
                "enabled": False,
                "api_key": "",
                "model": "gpt-4"
            },
            "anthropic": {
                "enabled": False,
                "api_key": "",
                "model": "claude-3"
            }
        }
    }
    
    # 配置数据库
    config.database = {
        "path": "database/game_data.db",
        "backup_interval": 3600,  # 1小时
        "max_backups": 24
    }
    
    # 配置缓存
    config.cache = {
        "ai_cache_size": 1000,
        "npc_cache_size": 500,
        "region_cache_size": 200
    }
```

### 3. game/core/game_engine.py - 核心引擎
```python
"""
LifeM游戏引擎核心实现
这是整个游戏系统的中央控制器
"""

import renpy
import threading
import time
from typing import Dict, List, Any, Optional

class LifeMGameEngine:
    """LifeM游戏引擎主类"""
    
    _instance = None
    _initialized = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if self._initialized:
            return
            
        self.world_manager = None
        self.npc_system = None
        self.ai_system = None
        self.economic_system = None
        self.ui_manager = None
        self.data_manager = None
        self.event_system = None
        
        self.is_running = False
        self.update_thread = None
        self.last_update_time = 0
        
        self._initialized = True
    
    def initialize(self) -> bool:
        """初始化游戏引擎及所有子系统"""
        try:
            renpy.log("Initializing LifeM Game Engine...")
            
            # 初始化数据管理器
            from data_manager import DataManager
            self.data_manager = DataManager()
            
            # 初始化世界管理器
            from world_manager import WorldManager
            self.world_manager = WorldManager(self.data_manager)
            
            # 初始化NPC系统
            from npc_system import NPCBehaviorSystem
            self.npc_system = NPCBehaviorSystem(self.data_manager)
            
            # 初始化AI系统
            from ai_integration import AIServiceManager
            self.ai_system = AIServiceManager()
            
            # 初始化经济系统
            from economic_system import EconomicSystem
            self.economic_system = EconomicSystem(self.data_manager)
            
            # 初始化事件系统
            from event_system import EventSystem
            self.event_system = EventSystem(self.data_manager)
            
            # 启动后台更新线程
            self.start_background_updates()
            
            renpy.log("LifeM Game Engine initialized successfully")
            return True
            
        except Exception as e:
            renpy.log(f"Failed to initialize LifeM Game Engine: {e}")
            return False
    
    def start_background_updates(self):
        """启动后台更新线程"""
        if self.update_thread and self.update_thread.is_alive():
            return
            
        self.is_running = True
        self.update_thread = threading.Thread(
            target=self._background_update_loop,
            daemon=True
        )
        self.update_thread.start()
    
    def _background_update_loop(self):
        """后台更新循环"""
        while self.is_running:
            try:
                current_time = time.time()
                
                # 检查是否需要更新（基于游戏时间）
                if self._should_update(current_time):
                    self._perform_update()
                    self.last_update_time = current_time
                
                # 休眠一段时间
                time.sleep(1)  # 每秒检查一次
                
            except Exception as e:
                renpy.log(f"Background update error: {e}")
                time.sleep(5)  # 出错时等待更长时间
    
    def _should_update(self, current_time: float) -> bool:
        """判断是否应该执行更新"""
        # 基于游戏时间流速决定更新频率
        time_scale = getattr(self.world_manager, 'time_scale', 1.0)
        update_interval = 60 / time_scale  # 基础间隔60秒
        
        return (current_time - self.last_update_time) >= update_interval
    
    def _perform_update(self):
        """执行游戏状态更新"""
        # 更新世界状态
        if self.world_manager:
            self.world_manager.update()
        
        # 更新NPC行为
        if self.npc_system:
            self.npc_system.update()
        
        # 更新经济系统
        if self.economic_system:
            self.economic_system.update()
        
        # 处理事件
        if self.event_system:
            self.event_system.process_events()
        
        # 触发UI更新
        renpy.restart_interaction()
    
    def shutdown(self):
        """关闭游戏引擎"""
        self.is_running = False
        if self.update_thread:
            self.update_thread.join(timeout=5)
        
        # 保存游戏状态
        if self.data_manager:
            self.data_manager.save_all_data()
        
        renpy.log("LifeM Game Engine shutdown complete")

# 全局游戏引擎实例
game_engine = LifeMGameEngine()
```

## 开发建议

### 1. 模块化开发
- 每个系统都应该是独立的模块
- 使用依赖注入减少耦合
- 建立清晰的接口定义

### 2. 测试策略
- 为每个核心模块编写单元测试
- 建立集成测试验证系统间交互
- 使用性能测试确保系统稳定性

### 3. 版本控制
- 使用Git进行版本控制
- 建立合理的分支策略
- 定期备份重要数据和配置

### 4. 文档维护
- 保持代码注释的及时更新
- 维护API文档和用户手册
- 记录重要的设计决策和变更

这个项目结构为LifeM游戏提供了清晰的组织架构，便于团队协作开发和后期维护。
