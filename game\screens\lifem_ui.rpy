# LifeM 用户界面屏幕定义
# 这个文件包含了LifeM游戏的自定义UI屏幕

# 游戏状态显示屏幕
screen lifem_status_overlay():
    """显示游戏状态的覆盖层"""
    
    # 只在游戏运行时显示
    if game_engine and game_engine.is_initialized:
        
        # 右上角状态面板
        frame:
            xalign 0.98
            yalign 0.02
            xsize 300
            ysize 150
            background "#000000aa"
            
            vbox:
                spacing 5
                xfill True
                
                # 当前时间
                text "游戏时间":
                    size 16
                    color "#ffffff"
                
                text "[game_engine.get_current_time_string()]":
                    size 14
                    color "#00ff00"
                
                # 游戏状态
                python:
                    paused = game_engine.game_state.get("paused", False)
                    time_scale = game_engine.game_state.get("time_scale", 1.0)
                
                text "状态: {'暂停' if paused else '运行中'}":
                    size 14
                    color "#ffff00" if paused else "#00ff00"
                
                text "流速: [time_scale]x":
                    size 14
                    color "#ffffff"
                
                # 控制按钮
                hbox:
                    spacing 10
                    
                    textbutton "暂停" if not paused else "继续":
                        text_size 12
                        action Function(toggle_game_pause)
                    
                    textbutton "设置":
                        text_size 12
                        action Show("lifem_settings")

# 游戏设置屏幕
screen lifem_settings():
    """游戏设置屏幕"""
    
    modal True
    
    frame:
        xalign 0.5
        yalign 0.5
        xsize 600
        ysize 400
        background "#222222"
        
        vbox:
            spacing 20
            xfill True
            yfill True
            
            # 标题
            text "LifeM 游戏设置":
                size 24
                color "#ffffff"
                xalign 0.5
            
            # 时间流速设置
            hbox:
                spacing 20
                
                text "时间流速:":
                    size 18
                    color "#ffffff"
                    yalign 0.5
                
                for scale in [0.5, 1.0, 2.0, 5.0]:
                    textbutton "[scale]x":
                        text_size 16
                        action [
                            Function(set_time_scale, scale),
                            Hide("lifem_settings")
                        ]
            
            # AI系统设置
            text "AI系统设置:":
                size 18
                color "#ffffff"
            
            hbox:
                spacing 20
                
                textbutton "测试对话AI":
                    action Function(test_dialogue_ai)
                
                textbutton "测试内容生成":
                    action Function(test_content_generation)
            
            # 调试选项
            text "调试选项:":
                size 18
                color "#ffffff"
            
            hbox:
                spacing 20
                
                textbutton "显示调试信息":
                    action ToggleVariable("debug_mode", True, False)
                
                textbutton "重置游戏状态":
                    action Function(reset_game_state)
            
            # 关闭按钮
            textbutton "关闭":
                xalign 0.5
                action Hide("lifem_settings")

# 调试信息显示屏幕
screen lifem_debug_info():
    """调试信息显示"""
    
    if config.developer or renpy.variant("pc"):
        
        frame:
            xalign 0.02
            yalign 0.02
            xsize 250
            background "#000000cc"
            
            vbox:
                spacing 3
                
                text "=== 调试信息 ===":
                    size 14
                    color "#ffff00"
                
                if game_engine:
                    text "引擎状态: 已初始化" if game_engine.is_initialized else "引擎状态: 未初始化":
                        size 12
                        color "#00ff00" if game_engine.is_initialized else "#ff0000"
                    
                    text "后台线程: 运行中" if game_engine.is_running else "后台线程: 已停止":
                        size 12
                        color "#00ff00" if game_engine.is_running else "#ff0000"
                    
                    python:
                        import threading
                        thread_count = threading.active_count()
                    
                    text "活跃线程数: [thread_count]":
                        size 12
                        color "#ffffff"
                
                else:
                    text "游戏引擎: 未加载":
                        size 12
                        color "#ff0000"

# Python函数定义
init python:
    
    def toggle_game_pause():
        """切换游戏暂停状态"""
        if game_engine:
            if game_engine.game_state.get("paused", False):
                game_engine.resume_game()
            else:
                game_engine.pause_game()
    
    def set_time_scale(scale):
        """设置时间流速"""
        if game_engine:
            game_engine.set_time_scale(scale)
    
    def test_dialogue_ai():
        """测试对话AI"""
        renpy.notify("对话AI测试功能尚未实现")
    
    def test_content_generation():
        """测试内容生成"""
        renpy.notify("内容生成测试功能尚未实现")
    
    def reset_game_state():
        """重置游戏状态"""
        if game_engine:
            game_engine.game_state = {
                "current_time": {"year": 2024, "month": 1, "day": 1, "hour": 8},
                "time_scale": 1.0,
                "paused": False
            }
            renpy.notify("游戏状态已重置")

# 默认显示状态覆盖层
default debug_mode = False

# 在游戏运行时自动显示状态覆盖层
label after_load:
    show screen lifem_status_overlay
    if debug_mode:
        show screen lifem_debug_info
    return

# 在游戏开始时显示状态覆盖层
label start_overlay:
    show screen lifem_status_overlay
    if debug_mode:
        show screen lifem_debug_info
    return
