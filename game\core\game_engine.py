"""
LifeM游戏引擎核心实现
这是整个游戏系统的中央控制器，负责协调各个子系统的运行
"""

import renpy
import threading
import time
import json
from typing import Dict, List, Any, Optional

class LifeMGameEngine:
    """LifeM游戏引擎主类 - 单例模式"""
    
    _instance = None
    _initialized = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if self._initialized:
            return
            
        # 子系统实例
        self.world_manager = None
        self.npc_system = None
        self.ai_system = None
        self.economic_system = None
        self.data_manager = None
        self.event_system = None
        
        # 运行状态
        self.is_running = False
        self.is_initialized = False
        self.update_thread = None
        self.last_update_time = 0
        
        # 游戏状态
        self.game_state = {
            "current_time": {"year": 2024, "month": 1, "day": 1, "hour": 8},
            "time_scale": 1.0,  # 时间流速倍率
            "paused": False
        }
        
        self._initialized = True
    
    def initialize(self) -> bool:
        """初始化游戏引擎及所有子系统"""
        if self.is_initialized:
            return True
            
        try:
            renpy.log("开始初始化LifeM游戏引擎...")
            
            # 初始化数据管理器
            self.data_manager = self._create_data_manager()
            
            # 初始化世界管理器
            self.world_manager = self._create_world_manager()
            
            # 初始化NPC系统
            self.npc_system = self._create_npc_system()
            
            # 初始化AI系统
            self.ai_system = self._create_ai_system()
            
            # 初始化经济系统
            self.economic_system = self._create_economic_system()
            
            # 初始化事件系统
            self.event_system = self._create_event_system()
            
            # 启动后台更新线程
            self.start_background_updates()
            
            self.is_initialized = True
            renpy.log("LifeM游戏引擎初始化成功")
            return True
            
        except Exception as e:
            renpy.log(f"LifeM游戏引擎初始化失败: {e}")
            return False
    
    def _create_data_manager(self):
        """创建数据管理器"""
        try:
            from .data_manager import DataManager
            return DataManager()
        except ImportError:
            renpy.log("数据管理器模块未找到，使用占位符")
            return MockDataManager()
    
    def _create_world_manager(self):
        """创建世界管理器"""
        try:
            from .world_manager import WorldManager
            return WorldManager(self.data_manager)
        except ImportError:
            renpy.log("世界管理器模块未找到，使用占位符")
            return MockWorldManager()
    
    def _create_npc_system(self):
        """创建NPC系统"""
        try:
            from .npc_system import NPCBehaviorSystem
            return NPCBehaviorSystem(self.data_manager)
        except ImportError:
            renpy.log("NPC系统模块未找到，使用占位符")
            return MockNPCSystem()
    
    def _create_ai_system(self):
        """创建AI系统"""
        try:
            from .ai_integration import AIServiceManager
            return AIServiceManager()
        except ImportError:
            renpy.log("AI系统模块未找到，使用占位符")
            return MockAISystem()
    
    def _create_economic_system(self):
        """创建经济系统"""
        try:
            from .economic_system import EconomicSystem
            return EconomicSystem(self.data_manager)
        except ImportError:
            renpy.log("经济系统模块未找到，使用占位符")
            return MockEconomicSystem()
    
    def _create_event_system(self):
        """创建事件系统"""
        try:
            from .event_system import EventSystem
            return EventSystem(self.data_manager)
        except ImportError:
            renpy.log("事件系统模块未找到，使用占位符")
            return MockEventSystem()
    
    def start_background_updates(self):
        """启动后台更新线程"""
        if self.update_thread and self.update_thread.is_alive():
            return
            
        self.is_running = True
        self.update_thread = threading.Thread(
            target=self._background_update_loop,
            daemon=True,
            name="LifeM-UpdateThread"
        )
        self.update_thread.start()
        renpy.log("后台更新线程已启动")
    
    def _background_update_loop(self):
        """后台更新循环"""
        while self.is_running:
            try:
                if not self.game_state["paused"]:
                    current_time = time.time()
                    
                    # 检查是否需要更新
                    if self._should_update(current_time):
                        self._perform_update()
                        self.last_update_time = current_time
                
                # 休眠
                time.sleep(1)
                
            except Exception as e:
                renpy.log(f"后台更新错误: {e}")
                time.sleep(5)
    
    def _should_update(self, current_time: float) -> bool:
        """判断是否应该执行更新"""
        time_scale = self.game_state.get("time_scale", 1.0)
        update_interval = 60 / time_scale  # 基础间隔60秒
        
        return (current_time - self.last_update_time) >= update_interval
    
    def _perform_update(self):
        """执行游戏状态更新"""
        try:
            # 推进游戏时间
            self._advance_game_time()
            
            # 更新各个子系统
            if self.world_manager:
                self.world_manager.update()
            
            if self.npc_system:
                self.npc_system.update()
            
            if self.economic_system:
                self.economic_system.update()
            
            if self.event_system:
                self.event_system.process_events()
            
            # 触发UI更新
            renpy.restart_interaction()
            
        except Exception as e:
            renpy.log(f"游戏更新错误: {e}")
    
    def _advance_game_time(self):
        """推进游戏时间"""
        current_time = self.game_state["current_time"]
        current_time["hour"] += 1
        
        if current_time["hour"] >= 24:
            current_time["hour"] = 0
            current_time["day"] += 1
            
            # 简单的月份处理
            days_in_month = 30  # 简化处理
            if current_time["day"] > days_in_month:
                current_time["day"] = 1
                current_time["month"] += 1
                
                if current_time["month"] > 12:
                    current_time["month"] = 1
                    current_time["year"] += 1
    
    def get_current_time_string(self) -> str:
        """获取当前游戏时间的字符串表示"""
        time_data = self.game_state["current_time"]
        return f"{time_data['year']}年{time_data['month']}月{time_data['day']}日 {time_data['hour']:02d}:00"
    
    def pause_game(self):
        """暂停游戏"""
        self.game_state["paused"] = True
        renpy.log("游戏已暂停")
    
    def resume_game(self):
        """恢复游戏"""
        self.game_state["paused"] = False
        renpy.log("游戏已恢复")
    
    def set_time_scale(self, scale: float):
        """设置时间流速"""
        self.game_state["time_scale"] = max(0.1, min(10.0, scale))
        renpy.log(f"时间流速设置为: {self.game_state['time_scale']}x")
    
    def shutdown(self):
        """关闭游戏引擎"""
        renpy.log("正在关闭LifeM游戏引擎...")
        
        self.is_running = False
        if self.update_thread:
            self.update_thread.join(timeout=5)
        
        # 保存游戏状态
        if self.data_manager:
            self.data_manager.save_all_data()
        
        renpy.log("LifeM游戏引擎已关闭")


# 占位符类，用于在相应模块未实现时提供基本功能
class MockDataManager:
    def save_all_data(self): pass

class MockWorldManager:
    def update(self): pass

class MockNPCSystem:
    def update(self): pass

class MockAISystem:
    def update(self): pass

class MockEconomicSystem:
    def update(self): pass

class MockEventSystem:
    def process_events(self): pass


# 全局游戏引擎实例
game_engine = LifeMGameEngine()
