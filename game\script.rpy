﻿# LifeM - 基于Ren'Py的AI驱动沙盒生活模拟游戏
# 主脚本文件

# 导入核心系统
init python:
    import sys
    import os

    # 添加核心模块路径
    core_path = os.path.join(config.gamedir, "core")
    if core_path not in sys.path:
        sys.path.insert(0, core_path)

    try:
        from game_engine import game_engine
        renpy.log("LifeM核心系统导入成功")
    except ImportError as e:
        renpy.log(f"LifeM核心系统导入失败: {e}")
        game_engine = None

# 声明游戏角色
define narrator = Character(None, kind=nvl)
define system = Character("系统", color="#00ff00")

# 游戏开始标签
label start:

    # 初始化游戏引擎
    python:
        if game_engine:
            init_success = game_engine.initialize()
            if not init_success:
                renpy.error("游戏引擎初始化失败")
        else:
            renpy.error("游戏引擎未找到")

    # 显示欢迎界面
    scene bg room

    # 显示状态覆盖层
    call start_overlay

    system "欢迎来到LifeM - AI驱动的沙盒生活模拟游戏！"

    system "游戏引擎已成功初始化。"

    if game_engine:
        system "当前游戏时间: [game_engine.get_current_time_string()]"

    menu:
        "您想要做什么？"

        "开始新游戏":
            jump new_game

        "查看游戏状态":
            jump game_status

        "测试AI系统":
            jump test_ai

        "退出游戏":
            return

label new_game:
    system "正在创建新的游戏世界..."

    # 这里将来会添加角色创建和世界生成逻辑
    system "游戏世界创建完成！"

    jump main_game_loop

label game_status:
    python:
        if game_engine:
            time_str = game_engine.get_current_time_string()
            paused = game_engine.game_state.get("paused", False)
            time_scale = game_engine.game_state.get("time_scale", 1.0)
        else:
            time_str = "未知"
            paused = True
            time_scale = 0

    system "=== 游戏状态 ==="
    system "当前时间: [time_str]"
    system "游戏状态: {'暂停' if paused else '运行中'}"
    system "时间流速: [time_scale]x"

    menu:
        "您想要做什么？"

        "暂停/恢复游戏" if not paused:
            python:
                if game_engine:
                    game_engine.pause_game()
            jump game_status

        "恢复游戏" if paused:
            python:
                if game_engine:
                    game_engine.resume_game()
            jump game_status

        "调整时间流速":
            jump adjust_time_scale

        "返回主菜单":
            jump start

label adjust_time_scale:
    menu:
        "选择时间流速："

        "0.5x (慢速)":
            python:
                if game_engine:
                    game_engine.set_time_scale(0.5)
            jump game_status

        "1x (正常)":
            python:
                if game_engine:
                    game_engine.set_time_scale(1.0)
            jump game_status

        "2x (快速)":
            python:
                if game_engine:
                    game_engine.set_time_scale(2.0)
            jump game_status

        "5x (极快)":
            python:
                if game_engine:
                    game_engine.set_time_scale(5.0)
            jump game_status

        "返回":
            jump game_status

label test_ai:
    system "AI系统测试功能尚未实现。"
    system "这里将来会添加AI对话、内容生成等测试功能。"

    jump start

label main_game_loop:
    # 主游戏循环 - 这里将来会实现主要的游戏玩法
    system "您现在进入了主游戏世界。"
    system "这里将来会实现完整的沙盒游戏体验。"

    menu:
        "您想要做什么？"

        "查看世界地图":
            system "世界地图功能尚未实现。"
            jump main_game_loop

        "与NPC交互":
            system "NPC交互功能尚未实现。"
            jump main_game_loop

        "查看经济状况":
            system "经济系统功能尚未实现。"
            jump main_game_loop

        "返回主菜单":
            jump start

# 游戏结束时的清理工作
label quit:
    python:
        if game_engine:
            game_engine.shutdown()

    return
