# LifeM Core Module
# 核心系统模块初始化文件

"""
LifeM游戏核心系统模块

这个模块包含了游戏的核心系统组件：
- 游戏引擎 (GameEngine)
- 世界管理器 (WorldManager)  
- NPC系统 (NPCSystem)
- AI集成 (AIIntegration)
- 经济系统 (EconomicSystem)
- 数据管理 (DataManager)
"""

__version__ = "1.0.0"
__author__ = "LifeM Development Team"

# 导入核心组件
try:
    from .game_engine import LifeMGameEngine
    from .data_manager import DataManager
    from .world_manager import WorldManager
    from .npc_system import NPCBehaviorSystem
    from .economic_system import EconomicSystem
    from .ai_integration import AIServiceManager
    
    __all__ = [
        'LifeMGameEngine',
        'DataManager', 
        'WorldManager',
        'NPCBehaviorSystem',
        'EconomicSystem',
        'AIServiceManager'
    ]
    
except ImportError as e:
    # 如果某些模块还未实现，先创建占位符
    import renpy
    renpy.log(f"Warning: Some core modules not yet implemented: {e}")
    
    __all__ = []
