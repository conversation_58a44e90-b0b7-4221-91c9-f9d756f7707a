# LifeM - AI驱动的沙盒生活模拟游戏

基于Ren'Py 8.4引擎开发的创新型沙盒生活模拟游戏，融合AI角色扮演、模拟经营、视觉小说等多种游戏类型。

## 项目概述

LifeM是一个雄心勃勃的游戏项目，旨在创造一个完全由AI驱动的动态虚拟世界。游戏的核心特色包括：

- **AI驱动的NPC系统**：每个NPC都具有独立的AI人格和行为模式
- **动态经济模拟**：基于NPC个体行为的真实经济系统
- **分层世界构建**：从全球政治到个人生活的完整模拟
- **多媒体AI生成**：动态生成对话、语音、图像和视频内容
- **无限制角色扮演**：玩家可以扮演任意角色，体验不同人生

## 当前开发状态

🚧 **项目处于早期开发阶段** 🚧

目前已完成：
- ✅ 项目需求文档和技术架构设计
- ✅ 基础的游戏引擎框架
- ✅ 核心数据结构定义
- ✅ 基本的用户界面
- ✅ 时间系统和游戏状态管理

正在开发中：
- 🔄 AI集成系统
- 🔄 NPC行为系统
- 🔄 经济模拟系统
- 🔄 世界地图系统

计划开发：
- ⏳ 多媒体内容生成
- ⏳ 动态事件系统
- ⏳ 社会关系网络
- ⏳ 沙盒编辑功能

## 快速开始

### 环境要求

- **Ren'Py 8.4+**：游戏引擎
- **Python 3.9+**：用于扩展功能
- **Windows/macOS/Linux**：支持跨平台运行

### 安装步骤

1. **下载Ren'Py**
   ```
   从 https://www.renpy.org/ 下载最新版本的Ren'Py
   ```

2. **克隆项目**
   ```bash
   git clone <repository-url>
   cd lifem
   ```

3. **在Ren'Py中打开项目**
   - 启动Ren'Py Launcher
   - 点击"刷新"按钮
   - 选择"lifem"项目
   - 点击"启动项目"

### 运行游戏

1. 在Ren'Py Launcher中选择LifeM项目
2. 点击"启动项目"按钮
3. 游戏将启动并显示欢迎界面
4. 使用右上角的状态面板监控游戏状态

### 基本操作

- **查看游戏状态**：右上角显示当前游戏时间和运行状态
- **暂停/继续游戏**：点击状态面板中的"暂停"/"继续"按钮
- **调整时间流速**：通过设置菜单调整游戏时间流速
- **开启调试模式**：在设置中启用调试信息显示

## 项目结构

```
lifem/
├── game/                      # Ren'Py游戏文件
│   ├── core/                  # 核心系统模块
│   │   ├── game_engine.py     # 游戏引擎
│   │   └── __init__.py        # 模块初始化
│   ├── screens/               # 用户界面屏幕
│   │   └── lifem_ui.rpy       # LifeM自定义UI
│   ├── script.rpy             # 主游戏脚本
│   ├── options.rpy            # 游戏配置
│   └── gui.rpy                # GUI设置
├── docs/                      # 项目文档
│   ├── project_requirements.md # 详细需求文档
│   └── project_structure.md    # 项目结构说明
└── README.md                  # 项目说明文件
```

## 开发指南

### 添加新功能

1. **创建新的系统模块**
   - 在`game/core/`目录下创建新的Python文件
   - 实现相应的类和方法
   - 在`game_engine.py`中集成新模块

2. **添加用户界面**
   - 在`game/screens/`目录下创建新的`.rpy`文件
   - 定义Ren'Py屏幕和相关函数
   - 在主脚本中调用新的屏幕

3. **扩展数据模型**
   - 在`game/data/`目录下定义新的数据结构
   - 更新数据管理器以支持新的数据类型

### 调试技巧

1. **启用调试模式**
   - 在游戏中打开设置菜单
   - 启用"显示调试信息"选项
   - 左上角将显示详细的调试信息

2. **查看日志**
   - Ren'Py会在项目目录下生成`log.txt`文件
   - 包含详细的运行日志和错误信息

3. **使用Ren'Py开发者工具**
   - 按`Shift+D`打开开发者菜单
   - 可以重新加载脚本、查看变量等

## 技术架构

### 核心组件

- **GameEngine**：游戏引擎核心，协调各个子系统
- **WorldManager**：世界状态管理，包括时间、地图、环境
- **NPCSystem**：NPC行为和AI决策系统
- **EconomicSystem**：经济模拟和市场动态
- **AIIntegration**：AI服务集成和内容生成

### 数据流

```
用户输入 → UI系统 → 游戏逻辑 → 数据更新
    ↓
AI系统 ← 上下文数据 ← 世界状态
    ↓
生成内容 → 展示给用户 → 影响游戏世界
```

## 贡献指南

我们欢迎社区贡献！请遵循以下步骤：

1. Fork项目仓库
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建Pull Request

### 代码规范

- 使用Python PEP 8代码风格
- 为所有函数和类添加文档字符串
- 保持代码模块化和可测试性
- 添加适当的错误处理和日志记录

## 许可证

本项目采用MIT许可证 - 详见 [LICENSE](LICENSE) 文件

## 联系方式

- 项目维护者：LifeM开发团队
- 问题反馈：通过GitHub Issues提交
- 讨论交流：[项目讨论区链接]

## 致谢

感谢以下项目和社区的启发：
- Ren'Py视觉小说引擎
- SillyTavern AI角色扮演系统
- 各种模拟经营游戏的设计理念

---

**注意**：本项目仍在积极开发中，功能和API可能会发生变化。建议定期查看更新日志和文档。
